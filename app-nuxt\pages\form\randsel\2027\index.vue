<script setup lang="ts">
import { ref, onMounted } from "vue";
import TheSchoolBagFormYearly from "~~/components/form/school-bag/TheSchoolBagFormYearly.vue";
// import TheSchoolBagFormLogined from "~~/components/form/school-bag/TheSchoolBagFormLogined.vue";
import { useRuntimeConfig, navigateTo } from "#app";
import { useAuthStore } from "~~/src/stores/auth";
import Products from "~~/src/models/Products";
import Orders from "~~/src/models/Orders";
import type RandselOrder from "~~/src/models/entry/RandselOrder";

import { useHead } from "unhead";

const isLoading = ref(true);

const authStore = useAuthStore();
const config = useRuntimeConfig();
const products = ref<TProduct[]>([]);

// 段階的表示制御用のリアクティブデータ
const currentStep = ref<'initial' | 'gender' | 'catalog'>('initial');
const selectedGender = ref(1);
const filteredProducts = ref<TProduct[]>([]);
const isLoadingProducts = ref(false);
const stepChanged = ref(false);


const isLogined = !!authStore.getAccessToken();
// const member = ref<Member>();
const orders = ref<RandselOrder[]>([]);

if (config.public.isProduction) {
    useHead({
        meta: [
            {
                name: "google-site-verification",
                content: "sd6BXRMajQVpR39NUe8I7YxWQyHFFqf0Qvpfx_0xKIQ",
            },
        ],
    });
}

// 性別に応じて商品をフィルタリングする関数
const filterProductsByGender = (gender: number) => {
    if (gender === 0) {
        filteredProducts.value = products.value;
    } else if (gender === 1) {
        // 男の子向け：性別指定なしメーカー（女の子限定メーカーを除く）
        filteredProducts.value = products.value.filter(product => {
            const brand = product.brand as { id?: number; name?: string; target_gender?: number; } | null;
            const brandTargetGender = brand?.target_gender || 3;
            return brandTargetGender !== 2;
        });
    } else if (gender === 2) {
        // 女の子向け：性別指定なしメーカー（男の子限定メーカーを除く）
        filteredProducts.value = products.value.filter(product => {
            const brand = product.brand as { id?: number; name?: string; target_gender?: number; } | null;
            const brandTargetGender = brand?.target_gender || 3;
            return brandTargetGender !== 1;
        });
    }
};

// 第1段階から第2段階への遷移（商品データ取得開始）
const toGenderSelection = async () => {
    currentStep.value = 'gender';

    // 商品データがまだ取得されていない場合のみ取得
    if (products.value.length === 0) {
        isLoadingProducts.value = true;
        try {
            const productList = await Products.create(config).getList({ year: 2027 });
            if (productList) {
                products.value = productList.map((product) => product.data);
            }
        } catch (error) {
            console.error("Error fetching products:", error);
        } finally {
            isLoadingProducts.value = false;
        }
    }
};

// 性別選択と第3段階への遷移
const selectGender = (gender: number) => {
    selectedGender.value = gender;
    filterProductsByGender(gender);
    currentStep.value = 'catalog';
};

// ステップ変更ハンドラー
const handleStepChanged = (isStepChanged: boolean) => {
    stepChanged.value = isStepChanged;
};

// 初期化処理を行う
onMounted(async () => {
    // ログインユーザーの場合のみ注文データを取得
    if (isLogined) {
        try {
            orders.value = await Orders.create(config).index();
            isLoading.value = false;
        } catch (error) {
            console.error("Error fetching orders:", error);
            authStore.clearAuth();
            navigateTo("/member/account/");
        }
    } else {
        isLoading.value = false;
    }
});
</script>

<template>
    <div>
        <div v-if="!stepChanged">
            <!-- 第1段階：初期表示 -->
            <v-row justify="center">
                <v-col cols="12" md="12" sm="12" class="px-0 text-center font-weight-bold text-primary text-h6">
                    <div>無料のカタログ請求でまるっと比較！</div>
                </v-col>
                <v-col cols="12" md="12" sm="12" class="pa-0 text-center text-body-2">
                    <div>2027年度（現在年中さん向け）請求フォーム</div>
                </v-col>
            </v-row>
            <v-row v-if="currentStep === 'initial'" justify="center">
                <v-col cols="12" md="4" sm="12">
                    <v-btn
                        block
                        size="large"
                        variant="outlined"
                        @click="toGenderSelection"
                    >
                        <!-- テキストをspanで囲む -->
                        <span class="button-label">次に進む</span>

                        <!-- アイコンをスロット内に配置し、CSSで位置を制御 -->
                        <v-icon
                        class="button-icon"
                        icon="mdi-chevron-right"
                        aria-hidden="true"
                        ></v-icon>
                    </v-btn>
                </v-col>
            </v-row>
    
            <!-- 第2段階：性別選択 -->
            <template v-if="currentStep === 'gender'">
                <!-- 商品データ読み込み中の表示 -->
                <div v-if="isLoadingProducts" class="text-center my-4">
                    <v-progress-circular indeterminate :size="40" />
                    <p class="mt-2">カタログ情報を読み込み中...</p>
                </div>
    
                <!-- 性別選択ボタン -->
                <template v-else>
                    <v-row justify="center">
                        <v-col cols="12" md="4" sm="12">
                            <v-btn
                                block
                                variant="outlined"
                                size="large"
                                @click="selectGender(1)"
                            >
                                <!-- テキストをspanで囲む -->
                                <span class="button-label">男の子にオススメ</span>
    
                                <!-- アイコンをスロット内に配置し、CSSで位置を制御 -->
                                <v-icon
                                class="button-icon"
                                icon="mdi-chevron-right"
                                aria-hidden="true"
                                ></v-icon>
                            </v-btn>
                        </v-col>
                    </v-row>
                    <v-row justify="center">
                        <v-col cols="12" md="4" sm="12">
                            <v-btn
                                block
                                variant="outlined"
                                size="large"
                                @click="selectGender(2)"
                            >
                                <!-- テキストをspanで囲む -->
                                <span class="button-label">女の子にオススメ</span>
    
                                <!-- アイコンをスロット内に配置し、CSSで位置を制御 -->
                                <v-icon
                                class="button-icon"
                                icon="mdi-chevron-right"
                                aria-hidden="true"
                                ></v-icon>
                            </v-btn>
                        </v-col>
                    </v-row>
                    <v-row justify="center">
                        <v-col cols="12" md="4" sm="12">
                            <v-btn
                                block
                                variant="outlined"
                                size="large"
                                @click="selectGender(0)"
                            >
                                <!-- テキストをspanで囲む -->
                                <span class="button-label">みんなにオススメ</span>
    
                                <!-- アイコンをスロット内に配置し、CSSで位置を制御 -->
                                <v-icon
                                class="button-icon"
                                icon="mdi-chevron-right"
                                aria-hidden="true"
                                ></v-icon>
                            </v-btn>
                        </v-col>
                    </v-row>
                
                </template>
            </template>
        </div>

        <!-- 第3段階：メーカー表示（カタログフォーム） -->
        <div v-if="currentStep === 'catalog' && !isLoading">
            <div v-if="isLogined">
                <the-school-bag-form-logined
                    v-if="filteredProducts.length > 0 && orders.length > 0"
                    :products-data="filteredProducts"
                    :order-data="orders"
                />
            </div>
            <div v-else>
                <the-school-bag-form-yearly
                    @step-changed="handleStepChanged"
                    v-if="filteredProducts.length"
                    :products-data="filteredProducts"
                    :selected-gender="selectedGender"
                />
            </div>
        </div>

        <!-- ローディング表示 -->
        <div v-if="isLoading" class="coverme-progress-circular">
            <v-progress-circular indeterminate :size="50" />
        </div>
    </div>
</template>

<style scoped>
.coverme-progress-circular {
    display: flex;
    justify-content: center;
    align-items: center;
}

.button-label {
  /* ラベルが常に中央に来るように調整 */
  width: 100%;
  text-align: center;
}

.button-icon {
  /* アイコンをボタンの右端に絶対配置 */
  position: absolute;
  right: 16px; /* 右からの距離 */
  top: 50%; /* 上下中央に配置するための準備 */
  transform: translateY(-50%); /* 上下中央に配置 */

  /* アイコンをクリックしてもボタンのイベントが発火するように */
  pointer-events: none;
}
</style>
